# Workflow: <PERSON><PERSON> thống tạo bài viết so sánh xe nâng cao

## Tổng quan

Nâng cấp hệ thống tạo bài viết so sánh xe với 4 tính năng mới:

1. <PERSON><PERSON><PERSON> trữ title và content riêng biệt từ JSON response
2. Upload ảnh lên AWS S3 và lưu trữ URLs
3. Thay thế placeholder ảnh bằng URLs từ S3
4. Chuyển đổi <PERSON> sang HTML

## Thay đổi Database

### Bảng car_comparisons (đã có)

- Đã có trường `html_content LONGTEXT NULL`
- Trường `title` đã tồn tại

### Bảng car_images (mới)

```sql
CREATE TABLE IF NOT EXISTS car_images (
    id INT AUTO_INCREMENT PRIMARY KEY,
    car_id VARCHAR(50) NOT NULL,
    car_name VARCHAR(500) NOT NULL,
    image_url VARCHAR(1000) NOT NULL,
    s3_key VARCHAR(500) NOT NULL,
    local_path VARCHAR(500) NULL,
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_car_id (car_id),
    INDEX idx_car_name (car_name),
    INDEX idx_uploaded_at (uploaded_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
```

## Thay đổi Code

### database_manager.py

**Thêm methods mới:**

- `save_comparison()`: Cập nhật để nhận title và content riêng biệt
- `update_comparison_html_content()`: Cập nhật HTML content
- `create_car_images_table()`: Tạo bảng lưu ảnh S3
- `save_car_image()`: Lưu thông tin ảnh S3
- `get_car_images_by_name()`: Lấy ảnh theo tên xe

### s3_uploader.py (mới)

**Class S3Uploader:**

- `upload_file()`: Upload một file lên S3
- `upload_multiple_files()`: Upload nhiều file
- `delete_file()`: Xóa file trên S3
- `check_bucket_exists()`: Kiểm tra bucket
- `create_s3_uploader_from_env()`: Tạo instance từ .env

### compare_cars.py

**Thay đổi chính:**

- `generate_article()`: Parse JSON response để tách title và content
- `convert_markdown_to_html()`: Convert Markdown sang HTML
- `replace_image_placeholders()`: Thay thế [ANH_XE_SO_1] và [ANH_XE_SO_2]
- Tích hợp S3 upload vào quy trình xử lý ảnh

## Dependencies mới

```
boto3==1.35.0
markdown==3.7
```

## Cấu hình AWS (.env)

```
ACCESS-KEY-ID=your_access_key
SECRET-ACCESS-KEY=your_secret_key
BUCKET-NAME=your_bucket_name
```

## Quy trình hoạt động mới

1. **Tạo bài viết**: AI trả về JSON với title và content
2. **Lưu database**: Lưu title và content riêng biệt
3. **Xử lý ảnh**: Download và process ảnh như cũ
4. **Upload S3**: Upload ảnh đã xử lý lên S3
5. **Lưu URLs**: Lưu URLs S3 vào bảng car_images
6. **Thay thế placeholder**: Thay TẤT CẢ [ANH_XE_SO_1] và [ANH_XE_SO_2] bằng URLs S3
7. **Convert HTML**: Chuyển Markdown (đã có ảnh) sang HTML
8. **Cập nhật DB**: Lưu content có ảnh và HTML content có ảnh

## Cách chạy

### Cài đặt dependencies

```powershell
pip install boto3==1.35.0 markdown==3.7
```

### Cấu hình AWS

Cập nhật file `.env` với AWS credentials:

```
ACCESS-KEY-ID=********************
SECRET-ACCESS-KEY=F4ERmM4tkplhbhBa/u9IjMsbPWHTRAbip3FH6fgs
BUCKET-NAME=xehoipro
```

### Chạy script

```powershell
python compare_cars.py
```

## Tính năng mới

### 1. JSON Response Parsing

- AI trả về: `{"title": "...", "content": "..."}`
- Code tự động parse và lưu riêng biệt
- Fallback nếu JSON không hợp lệ

### 2. S3 Integration

- Upload ảnh đã xử lý lên AWS S3
- Tạo URLs public cho ảnh
- Lưu metadata vào database
- Xử lý lỗi upload gracefully

### 3. Image Placeholder Replacement

- Thay TẤT CẢ `[ANH_XE_SO_1]` bằng ảnh xe thứ nhất (có thể có 2-3 lần xuất hiện)
- Thay TẤT CẢ `[ANH_XE_SO_2]` bằng ảnh xe thứ hai (có thể có 2-3 lần xuất hiện)
- Sử dụng URLs từ S3
- Format: `![Tên xe](URL)`
- Logic: Sử dụng `replace()` để thay thế toàn bộ, không chỉ 1 lần

### 4. Markdown to HTML

- Sử dụng thư viện `markdown` với extensions
- Hỗ trợ tables, code highlighting, TOC
- Lưu vào trường `html_content`
- Fallback nếu conversion lỗi

## Error Handling

- S3 upload lỗi: Tiếp tục với ảnh local
- JSON parse lỗi: Sử dụng title mặc định
- Markdown conversion lỗi: Giữ nguyên Markdown
- Database lỗi: Log và retry

## Monitoring

- Log chi tiết cho từng bước
- Track upload success/failure
- Monitor placeholder replacement
- HTML conversion status

## Cải tiến mới nhất (v2.0)

### Thay đổi chính:

1. **Multiple Placeholder Support**: Thay thế TẤT CẢ placeholder thay vì chỉ 1 lần
2. **Improved Processing Order**: Thay placeholder trước, convert HTML sau
3. **Enhanced HTML Output**: Thêm CSS classes cho responsive images
4. **Better Fallback**: Đảm bảo placeholder được thay thế ngay cả khi S3 lỗi

### Technical Details:

- `replace_image_placeholders()`: Sử dụng `content.count()` để đếm và `replace()` để thay tất cả
- `convert_markdown_to_html()`: Thêm CSS classes và cải thiện table formatting
- Fallback logic: Luôn thay placeholder và convert HTML, kể cả khi upload S3 lỗi

## Mở rộng tương lai

- CDN integration cho ảnh
- Image optimization trước upload
- Batch processing cho nhiều bài viết
- API endpoint để lấy HTML content
- Cache HTML content
- Multiple image per placeholder (random selection)
