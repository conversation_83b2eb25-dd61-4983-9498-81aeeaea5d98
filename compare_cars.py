"""
Công cụ tạo bài viết so sánh hai xe từ dữ liệu trong DB và OpenAI API
"""
import logging
import sys
import os
from datetime import datetime
from typing import Optional, Tuple

from config import LOGGING_CONFIG, LLM_CONFIG, IMAGE_CONFIG
from database_manager import DatabaseManager
from s3_uploader import create_s3_uploader_from_env

# OpenAI SDK hiện tại (2024-2025)
try:
    from openai import OpenAI
except Exception:  # fallback nếu gói cũ
    OpenAI = None


def setup_logging():
    logging.basicConfig(
        level=getattr(logging, LOGGING_CONFIG['level']),
        format=LOGGING_CONFIG['format'],
        handlers=[
            logging.FileHandler(LOGGING_CONFIG['file'], encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )


def pick_two_cars(db: DatabaseManager, tolerance: float = 0.2) -> Optional[Tuple[dict, dict]]:
    """Chọn ngẫu nhiên 2 xe theo tiêu chí: xe2 có giá trong ±20% của xe1"""
    logger = logging.getLogger(__name__)
    car1 = db.get_random_car()
    if not car1:
        logger.warning("Không tìm được xe thứ nhất")
        return None
    car2 = db.get_random_car_by_price_range(car1['car_price'], tolerance=tolerance, exclude_car_id=car1['car_id'])
    if not car2:
        logger.warning("Không tìm được xe thứ hai phù hợp giá ±%.0f%%" % (tolerance * 100))
        return None
    return car1, car2


def build_prompt(car1: dict, car2: dict) -> str:
    """Tạo prompt chi tiết để sinh bài viết so sánh"""
    def car_brief(c: dict) -> str:
        return f"{c.get('car_name')} (Giá: {c.get('car_price_text') or c.get('car_price')} VND) - Link: {c.get('car_link') or 'N/A'}"

    prompt = f"""
Bạn là chuyên gia ô tô tại Việt Nam. Viết một bài so sánh tự nhiên, chuẩn SEO, tránh văn phong máy móc, giữa 2 mẫu xe sau:
- Xe A: {car_brief(car1)}
- Xe B: {car_brief(car2)}

Yêu cầu:
1) Mở bài: bối cảnh, đối tượng phù hợp, nêu tiêu chí so sánh.
2) Tổng quan nhanh từng xe: thương hiệu, phân khúc, thế mạnh thường được nhắc tới.
3) Bảng so sánh thông số kỹ thuật dưới dạng bảng Markdown với các hàng gợi ý: Giá tham khảo, Động cơ/hộp số, Công suất/mô-men xoắn, Mức tiêu hao nhiên liệu ước tính, Kích thước (DxRxC, chiều dài cơ sở), Khoảng sáng gầm, Trang bị an toàn chủ động/bị động, Tiện nghi nổi bật, Năm sản xuất/đời, Số km (nếu có). Nếu thiếu dữ liệu, ghi "—".
4) Phân tích ưu - nhược điểm mỗi xe theo góc nhìn người dùng tại VN (chi phí nuôi xe, độ bền, bán lại, dịch vụ, phụ tùng...).
5) So sánh cảm giác lái, cách âm, thoải mái hàng ghế, công nghệ hỗ trợ lái (nếu có, nêu theo mức độ phổ biến).
6) Kết luận: khuyến nghị rõ ràng theo từng nhóm khách hàng (gia đình đô thị, chạy dịch vụ, đam mê lái xe, tiết kiệm nhiên liệu...).
7) Viết tự nhiên, có ví dụ đời sống; tránh rập khuôn; cấu trúc rõ ràng với các tiêu đề phụ (##, ###); dùng tiếng Việt.
8) Độ dài ~1000-1400 từ.
9) Trong giữa nội dung bài viết thêm các tag như: [ANH_XE_SO_1] và [ANH_XE_SO_2] (căn giữa) để chèn ảnh xe A và xe B vào bài viết (mỗi xe sẽ có 2-3 tag) ngẫu nhiên trong nội dung bài viết
10) Không có các từ giới thiệu như "Mở bài", để bài viết tự nhiên nhất.
11) Nội dung trả về dạng JSON như sau 
{{
        "title": "Tiêu đề bài viết",
    "content": "Nội dung bài viết"
}}
"""
    return prompt.strip()


def generate_article(car1: dict, car2: dict) -> dict:
    logger = logging.getLogger(__name__)
    if LLM_CONFIG.get('provider') != 'openai' or not LLM_CONFIG.get('api_key'):
        raise RuntimeError("Chưa cấu hình OpenAI API key (OPENAI_API_KEY)")

    if OpenAI is None:
        raise RuntimeError("Chưa cài gói openai >= 1.0.0. Hãy chạy: pip install openai")

    client = OpenAI(api_key=LLM_CONFIG['api_key'])
    system_msg = "Bạn là biên tập viên ô tô dày dạn kinh nghiệm, viết đúng ngữ cảnh thị trường Việt Nam."
    user_msg = build_prompt(car1, car2)
    try:
        # Sử dụng Responses API mới của OpenAI nếu khả dụng
        try:
            resp = client.responses.create(
                model=LLM_CONFIG['model'],
                # temperature=LLM_CONFIG['temperature'],
                # max_output_tokens=LLM_CONFIG['max_tokens'],
                input=[
                    {"role": "system", "content": system_msg},
                    {"role": "user", "content": user_msg},
                ],
            )
            # Trích xuất text
            content = ""
            if hasattr(resp, 'output') and resp.output:
                for item in resp.output:
                    if getattr(item, 'type', '') == 'message' and item.content:
                        for block in item.content:
                            if getattr(block, 'type', '') == 'output_text':
                                content += block.text
            if not content:
                # Fallback một số phiên bản SDK
                content = getattr(resp, 'output_text', '') or str(resp)
        except Exception:
            # Fallback sang Chat Completions
            chat = client.chat.completions.create(
                model=LLM_CONFIG['model'],
                # temperature=LLM_CONFIG['temperature'],
                # max_tokens=LLM_CONFIG['max_tokens'],
                messages=[
                    {"role": "system", "content": system_msg},
                    {"role": "user", "content": user_msg},
                ],
            )
            content = chat.choices[0].message.content

        if not content:
            raise RuntimeError("Không nhận được nội dung từ OpenAI")

        # Parse JSON response
        try:
            import json
            # Tìm JSON trong response (có thể có text khác xung quanh)
            start_idx = content.find('{')
            end_idx = content.rfind('}') + 1
            if start_idx != -1 and end_idx > start_idx:
                json_str = content[start_idx:end_idx]
                parsed_data = json.loads(json_str)

                # Validate required fields
                if 'title' not in parsed_data or 'content' not in parsed_data:
                    raise ValueError("JSON response thiếu trường 'title' hoặc 'content'")

                return {
                    'title': parsed_data['title'].strip(),
                    'content': parsed_data['content'].strip()
                }
            else:
                raise ValueError("Không tìm thấy JSON hợp lệ trong response")
        except (json.JSONDecodeError, ValueError) as e:
            logger.warning(f"Không thể parse JSON response: {e}")
            # Fallback: trả về content gốc với title mặc định
            return {
                'title': f"So sánh {car1.get('car_name', 'Xe 1')} và {car2.get('car_name', 'Xe 2')}",
                'content': content
            }
    except Exception as e:
        logger.error(f"Lỗi gọi OpenAI: {e}")
        raise


def convert_markdown_to_html(markdown_content: str) -> str:
    """
    Chuyển đổi nội dung Markdown sang HTML

    Args:
        markdown_content: Nội dung Markdown

    Returns:
        Nội dung HTML
    """
    logger = logging.getLogger(__name__)

    try:
        import markdown

        # Tạo Markdown instance với các extension hữu ích
        md = markdown.Markdown(
            extensions=[
                'extra',      # Bao gồm tables, fenced_code, footnotes, etc.
                'codehilite',  # Syntax highlighting cho code blocks
                'toc',        # Table of contents
                'nl2br',      # Newline to <br>
            ],
            extension_configs={
                'codehilite': {
                    'css_class': 'highlight',
                    'use_pygments': False,  # Sử dụng CSS classes thay vì inline styles
                },
                'toc': {
                    'permalink': True,
                }
            }
        )

        # Convert Markdown to HTML
        html_content = md.convert(markdown_content)

        # Cải thiện HTML output
        html_content = html_content.replace('<table>', '<table border="1" cellpadding="2" class="table table-striped">')
        html_content = html_content.replace('&para;', '')

        # Thêm class cho images để responsive
        html_content = html_content.replace('<img ', '<img class="img-responsive" ')

        logger.info("Đã chuyển đổi Markdown sang HTML thành công")
        return html_content

    except ImportError:
        logger.error("Chưa cài đặt thư viện markdown. Chạy: pip install markdown")
        return markdown_content  # Fallback: trả về Markdown gốc
    except Exception as e:
        logger.error(f"Lỗi chuyển đổi Markdown sang HTML: {e}")
        return markdown_content  # Fallback: trả về Markdown gốc


def replace_image_placeholders(content: str, car1_name: str, car2_name: str, db: DatabaseManager) -> str:
    """
    Thay thế placeholder ảnh trong content bằng URLs từ S3 (mỗi lần thay thế dùng ảnh khác nhau)

    Args:
        content: Nội dung bài viết có chứa placeholder
        car1_name: Tên xe thứ nhất
        car2_name: Tên xe thứ hai
        db: Database manager instance

    Returns:
        Content đã thay thế tất cả placeholder với ảnh khác nhau
    """
    logger = logging.getLogger(__name__)

    try:
        # Lấy nhiều ảnh của xe 1 và xe 2 từ database
        car1_images = db.get_car_images_by_name(car1_name, limit=5)
        car2_images = db.get_car_images_by_name(car2_name, limit=5)

        # Đếm số lần xuất hiện của mỗi placeholder
        car1_count = content.count('[ANH_XE_SO_1]')
        car2_count = content.count('[ANH_XE_SO_2]')

        logger.info(f"Tìm thấy {car1_count} placeholder [ANH_XE_SO_1] và {car2_count} placeholder [ANH_XE_SO_2]")
        logger.info(f"Có {len(car1_images)} ảnh xe 1 và {len(car2_images)} ảnh xe 2")

        # Thay thế tuần tự placeholder cho xe 1
        if car1_images and car1_count > 0:
            car1_index = 0
            for i in range(car1_count):
                # Lấy ảnh theo thứ tự, nếu hết thì lặp lại từ đầu
                current_image = car1_images[car1_index % len(car1_images)]
                car1_image_url = current_image['image_url']

                # Thay thế chỉ lần xuất hiện đầu tiên của placeholder
                content = content.replace('[ANH_XE_SO_1]', f'![{car1_name}]({car1_image_url})', 1)
                logger.info(f"Thay thế [ANH_XE_SO_1] lần {i+1} bằng ảnh {car1_index+1}: {car1_image_url}")

                car1_index += 1
        elif car1_count > 0:
            logger.warning(f"Không tìm thấy ảnh cho xe {car1_name} nhưng có {car1_count} placeholder [ANH_XE_SO_1]")

        # Thay thế tuần tự placeholder cho xe 2
        if car2_images and car2_count > 0:
            car2_index = 0
            for i in range(car2_count):
                # Lấy ảnh theo thứ tự, nếu hết thì lặp lại từ đầu
                current_image = car2_images[car2_index % len(car2_images)]
                car2_image_url = current_image['image_url']

                # Thay thế chỉ lần xuất hiện đầu tiên của placeholder
                content = content.replace('[ANH_XE_SO_2]', f'![{car2_name}]({car2_image_url})', 1)
                logger.info(f"Thay thế [ANH_XE_SO_2] lần {i+1} bằng ảnh {car2_index+1}: {car2_image_url}")

                car2_index += 1
        elif car2_count > 0:
            logger.warning(f"Không tìm thấy ảnh cho xe {car2_name} nhưng có {car2_count} placeholder [ANH_XE_SO_2]")

        # Kiểm tra xem còn placeholder nào chưa được thay thế không
        remaining_placeholders = content.count('[ANH_XE_SO_1]') + content.count('[ANH_XE_SO_2]')
        if remaining_placeholders > 0:
            logger.warning(f"Còn {remaining_placeholders} placeholder chưa được thay thế")
        else:
            logger.info("Đã thay thế thành công tất cả placeholder ảnh với ảnh khác nhau")

        return content

    except Exception as e:
        logger.error(f"Lỗi thay thế placeholder ảnh: {e}")
        return content  # Trả về content gốc nếu có lỗi


def create_and_save_comparison(max_attempts: int = 8) -> bool:
    logger = logging.getLogger(__name__)
    db = DatabaseManager()

    try:
        if not db.connect():
            logger.error("Không thể kết nối database")
            return False

        # Đảm bảo có bảng lưu bài viết
        if not db.create_comparisons_table():
            logger.error("Không thể tạo bảng car_comparisons")
            return False

        # Lặp chọn cặp xe mới nếu đã có bài viết
        attempt = 0
        while attempt < max_attempts:
            attempt += 1
            picked = pick_two_cars(db, tolerance=0.2)
            if not picked:
                logger.warning(f"[Thử {attempt}/{max_attempts}] Không chọn được cặp xe phù hợp")
                continue
            car1, car2 = picked
            logger.info(f"[Thử {attempt}/{max_attempts}] Cặp xe: '{car1['car_name']}' vs '{car2['car_name']}'")

            if db.comparison_exists(car1['car_name'], car2['car_name']):
                logger.info("Cặp xe này đã có bài viết so sánh, chọn lại...")
                continue

            # Sinh bài viết
            article_data = generate_article(car1, car2)

            # Lưu
            comp_id = db.save_comparison(
                car1_name=car1['car_name'],
                car2_name=car2['car_name'],
                title=article_data['title'],
                content=article_data['content']
            )
            if not comp_id:
                logger.error("Lưu bài viết thất bại, thử lại cặp khác...")
                continue

            # Xử lý ảnh cho 2 xe (không để fail chặn bài viết)
            try:
                from image_pipeline import (
                    fetch_and_process_images_for_car,
                    ImageResult,
                )
                want = IMAGE_CONFIG.get('per_car_images', 5)
                imgs1 = fetch_and_process_images_for_car(car1['car_name'], want=want)
                imgs2 = fetch_and_process_images_for_car(car2['car_name'], want=want)
                # Gộp 4-5 ảnh: ưu tiên 2-3 ảnh xe A + 2-3 ảnh xe B
                merged: list[ImageResult] = []
                merged.extend(imgs1[:max(2, want//2)])
                merged.extend(imgs2[:max(2, want//2)])
                merged = merged[:max(4, min(5, want))]

                if merged:
                    # Upload ảnh lên S3
                    s3_uploader = create_s3_uploader_from_env()
                    if s3_uploader:
                        # Tạo bảng car_images nếu chưa có
                        db.create_car_images_table()

                        # Upload từng ảnh lên S3 và lưu thông tin
                        for img_result in merged:
                            if img_result.local_path and os.path.exists(img_result.local_path):
                                upload_result = s3_uploader.upload_file(img_result.local_path)
                                if upload_result:
                                    # Xác định xe nào (dựa vào thứ tự trong merged)
                                    car_name = car1['car_name'] if merged.index(img_result) < len(imgs1) else car2['car_name']
                                    car_id = car1.get('car_id', '') if merged.index(img_result) < len(imgs1) else car2.get('car_id', '')

                                    # Lưu thông tin ảnh S3 vào DB
                                    # Extract S3 key from URL (format: https://bucket.s3.region.amazonaws.com/key)
                                    s3_key = '/'.join(upload_result.split('/')[3:]) if '/' in upload_result else upload_result.split('/')[-1]
                                    db.save_car_image(
                                        car_id=car_id,
                                        car_name=car_name,
                                        image_url=upload_result,
                                        s3_key=s3_key,
                                        local_path=img_result.local_path
                                    )
                                    logger.info(f"Đã upload ảnh lên S3: {upload_result}")

                    # Thay thế placeholder ảnh bằng URLs từ S3
                    content_with_images = replace_image_placeholders(
                        article_data['content'],
                        car1['car_name'],
                        car2['car_name'],
                        db
                    )
                    # Chuyển đổi Markdown sang HTML
                    html_content = convert_markdown_to_html(content_with_images)

                    # Cập nhật content vào DB (lưu nội dung có ảnh từ S3)
                    db.update_comparison_content(comparison_id=comp_id, content=content_with_images)
                    # Cập nhật HTML content vào DB
                    db.update_comparison_html_content(comparison_id=comp_id, html_content=html_content)
                    # Cập nhật image_paths vào DB
                    db.update_comparison_images(comparison_id=comp_id, image_paths=[i.local_path for i in merged])
            except Exception as e:
                logger.warning(f"Xử lý ảnh gặp lỗi nhưng không ảnh hưởng bài viết: {e}")

            # Đảm bảo luôn có HTML content (fallback nếu không có ảnh hoặc S3 upload lỗi)
            try:
                # Thay thế placeholder trong content gốc (fallback)
                current_content = replace_image_placeholders(
                    article_data['content'],
                    car1['car_name'],
                    car2['car_name'],
                    db
                )
                # Convert sang HTML
                html_content = convert_markdown_to_html(current_content)
                # Cập nhật cả content và HTML content
                db.update_comparison_content(comparison_id=comp_id, content=current_content)
                db.update_comparison_html_content(comparison_id=comp_id, html_content=html_content)
                logger.info("Đã cập nhật content và HTML content (fallback)")
            except Exception as e:
                logger.warning(f"Lỗi cập nhật content fallback: {e}")

            logger.info(f"Tạo và lưu bài viết so sánh thành công (ID={comp_id})")
            return True

        logger.error("Hết lượt thử nhưng chưa tạo được bài viết mới")
        return False
    except Exception as e:
        logger.error(f"Lỗi quy trình tạo bài viết: {e}")
        return False
    finally:
        db.disconnect()


if __name__ == "__main__":
    setup_logging()
    ok = create_and_save_comparison()
    sys.exit(0 if ok else 1)
