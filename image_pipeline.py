"""
Pipeline tì<PERSON> k<PERSON>, tả<PERSON> và xử lý ảnh xe, sau đ<PERSON> chèn vào bài viết Markdown
"""
from __future__ import annotations
import aiohttp
import asyncio
from config import IMAGE_CONFIG
import io
import os
import random
import time
import logging
from dataclasses import dataclass
from typing import List, Optional, Tuple

import requests
from PIL import Image, ImageDraw, ImageFont
from rembg import remove
from dotenv import load_dotenv
from libs.image_modifier import modify_image, add_gaussian_blur, modify_lsb_steganography

# Load environment variables
load_dotenv()


@dataclass
class ImageResult:
    url: str
    local_path: str


class ImageSearchError(Exception):
    pass


class ImageProcessor:
    def __init__(self, logger: Optional[logging.Logger] = None):
        self.logger = logger or logging.getLogger(__name__)
        self.timeout = IMAGE_CONFIG['download_timeout']

    def download_image(self, url: str, save_path: str) -> Optional[str]:
        try:
            os.makedirs(os.path.dirname(save_path), exist_ok=True)
            resp = requests.get(url, timeout=self.timeout)
            if resp.status_code != 200:
                self.logger.warning(f"Tải ảnh thất bại: {url} ({resp.status_code})")
                return None
            with open(save_path, 'wb') as f:
                f.write(resp.content)
            return save_path
        except Exception as e:
            self.logger.error(f"Lỗi tải ảnh: {e}")
            return None

    def replace_background(self, input_path: str, output_path: str) -> Optional[str]:
        """
        Xử lý ảnh: tách nền (nếu được bật) hoặc chỉ áp dụng image_modifier
        """
        try:
            # Kiểm tra biến môi trường để quyết định có thực hiện background removal hay không
            enable_bg_removal = os.getenv('ENABLE_BACKGROUND_REMOVAL', 'false').lower() == 'true'

            if enable_bg_removal:
                # Logic cũ: tách nền và thay background
                fg = Image.open(input_path).convert('RGBA')
                fg_rgba = Image.open(io.BytesIO(remove(fg.tobytes()))) if False else remove(fg)
                # Lưu ý: rembg.remove hỗ trợ trực tiếp PIL Image -> numpy bytes hoặc bytes; để đơn giản, gọi remove(fg) trả về Image khi lib hỗ trợ.
                # Nếu môi trường trả về bytes, cần chuyển lại Image từ bytes.
                if isinstance(fg_rgba, (bytes, bytearray)):
                    fg_rgba = Image.open(io.BytesIO(fg_rgba)).convert('RGBA')
                else:
                    # một số phiên bản trả về numpy array -> chuyển Image
                    try:
                        from PIL import Image as _Image
                        if not isinstance(fg_rgba, Image.Image):
                            fg_rgba = _Image.fromarray(fg_rgba).convert('RGBA')
                    except Exception:
                        pass

                # Chọn background random
                bg_dir = IMAGE_CONFIG['backgrounds_dir']
                if not os.path.isdir(bg_dir):
                    os.makedirs(bg_dir, exist_ok=True)
                candidates = [f for f in os.listdir(bg_dir) if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
                if not candidates:
                    self.logger.warning('Không có ảnh nền trong assets/backgrounds, dùng nền trắng')
                    bg = Image.new('RGBA', fg_rgba.size, (255, 255, 255, 255))
                else:
                    bg_path = os.path.join(bg_dir, random.choice(candidates))
                    bg = Image.open(bg_path).convert('RGBA')
                    # Resize background theo tỷ lệ foreground
                    bg = bg.resize(fg_rgba.size)

                # Ghép ảnh: dán fg lên bg
                composed = Image.alpha_composite(bg, fg_rgba)

                # Thêm watermark
                wm_text = IMAGE_CONFIG.get('watermark_text', 'xehoi.pro')
                composed = self.add_watermark(composed, wm_text)

                os.makedirs(os.path.dirname(output_path), exist_ok=True)
                composed.convert('RGB').save(output_path, format='JPEG', quality=92)
            else:
                # Logic mới: chỉ sử dụng image_modifier để xử lý ảnh
                self.logger.info(f"Background removal bị tắt, sử dụng image_modifier cho {input_path}")

                # Tạo thư mục output nếu chưa tồn tại
                os.makedirs(os.path.dirname(output_path), exist_ok=True)

                # Tạo file tạm thời để modify_image xử lý
                temp_path = output_path.replace('.jpg', '_temp.jpg')

                # Sử dụng modify_image từ libs/image_modifier.py
                modified_path = modify_image(input_path, temp_path)

                # Thêm watermark vào ảnh đã được modify
                with Image.open(modified_path) as img:
                    wm_text = IMAGE_CONFIG.get('watermark_text', 'xehoi.pro')
                    watermarked = self.add_watermark(img.convert('RGBA'), wm_text)
                    watermarked.convert('RGB').save(output_path, format='JPEG', quality=92)

                # Xóa file tạm thời
                if os.path.exists(temp_path) and temp_path != output_path:
                    os.remove(temp_path)

                self.logger.info(f"Đã xử lý ảnh bằng image_modifier: {output_path}")

            return output_path
        except Exception as e:
            self.logger.error(f"Lỗi xử lý ảnh cho {input_path}: {e}")
            return None

    def add_watermark(self, img_rgba: Image.Image, text: str = None) -> Image.Image:
        """
        Thêm watermark hình ảnh (logo) vào góc dưới bên phải của ảnh.
        Fallback về text watermark nếu logo không tải được.
        """
        try:
            img = img_rgba.copy()
            logo_path = os.path.join('assets', 'logo.png')

            # Kiểm tra file logo có tồn tại không
            if os.path.exists(logo_path):
                try:
                    # Mở logo và chuyển sang RGBA
                    with Image.open(logo_path) as logo:
                        logo = logo.convert('RGBA')

                        # Tính toán kích thước watermark (12% chiều rộng ảnh gốc)
                        watermark_width = int(img.width * 0.12)

                        # Giữ tỷ lệ khung hình của logo
                        logo_ratio = logo.height / logo.width
                        watermark_height = int(watermark_width * logo_ratio)

                        # Resize logo
                        logo_resized = logo.resize((watermark_width, watermark_height), Image.Resampling.LANCZOS)

                        # Áp dụng độ trong suốt (alpha = 180/255 ≈ 70%)
                        if logo_resized.mode == 'RGBA':
                            # Tạo mask với độ trong suốt
                            alpha = logo_resized.split()[-1]  # Lấy alpha channel
                            alpha = alpha.point(lambda p: int(p * 0.7))  # Giảm opacity xuống 70%
                            logo_resized.putalpha(alpha)
                        else:
                            # Nếu logo không có alpha channel, tạo alpha channel mới
                            logo_resized = logo_resized.convert('RGBA')
                            alpha = Image.new('L', logo_resized.size, int(255 * 0.7))
                            logo_resized.putalpha(alpha)

                        # Tính toán vị trí (góc dưới bên phải với margin)
                        margin = max(15, img.width // 80)  # Margin linh hoạt
                        x = img.width - watermark_width - margin
                        y = img.height - watermark_height - margin

                        # Paste logo vào ảnh
                        img.paste(logo_resized, (x, y), logo_resized)

                        self.logger.info(f"Đã thêm watermark logo kích thước {watermark_width}x{watermark_height}")
                        return img

                except Exception as logo_error:
                    self.logger.warning(f"Lỗi xử lý logo watermark: {logo_error}, fallback về text watermark")
            else:
                self.logger.warning(f"Không tìm thấy file logo tại {logo_path}, sử dụng text watermark")

            # Fallback: sử dụng text watermark như cũ
            if not text:
                text = IMAGE_CONFIG.get('watermark_text', 'xehoi.pro')

            draw = ImageDraw.Draw(img)
            try:
                font = ImageFont.truetype("arial.ttf", max(16, img.height // 40))
            except Exception:
                font = ImageFont.load_default()
            margin = max(10, img.width // 100)

            # Sử dụng textbbox thay vì textsize (deprecated)
            try:
                bbox = draw.textbbox((0, 0), text, font=font)
                text_w = bbox[2] - bbox[0]
                text_h = bbox[3] - bbox[1]
            except AttributeError:
                # Fallback cho phiên bản PIL cũ
                text_w, text_h = draw.textsize(text, font=font)

            x = img.width - text_w - margin
            y = img.height - text_h - margin

            # Nền mờ cho text để nổi bật
            overlay = Image.new('RGBA', (text_w + 8, text_h + 4), (0, 0, 0, 90))
            img.paste(overlay, (x - 4, y - 2), overlay)
            draw.text((x, y), text, font=font, fill=(255, 255, 255, 210))

            return img

        except Exception as e:
            self.logger.error(f"Lỗi thêm watermark: {e}")
            return img_rgba

    def apply_image_modifications(self, input_path: str, output_path: str,
                                  use_blur: bool = False, use_lsb: bool = False) -> Optional[str]:
        """
        Áp dụng các chỉnh sửa ảnh từ image_modifier.py

        Args:
            input_path: Đường dẫn ảnh đầu vào
            output_path: Đường dẫn ảnh đầu ra
            use_blur: Có áp dụng Gaussian blur không
            use_lsb: Có áp dụng LSB steganography không

        Returns:
            Đường dẫn ảnh đã xử lý hoặc None nếu lỗi
        """
        try:
            current_path = input_path
            temp_paths = []

            # Tạo thư mục output nếu chưa tồn tại
            os.makedirs(os.path.dirname(output_path), exist_ok=True)

            # 1. Áp dụng modify_image (luôn luôn)
            temp1 = output_path.replace('.jpg', '_temp1.jpg')
            current_path = modify_image(current_path, temp1)
            temp_paths.append(temp1)

            # 2. Áp dụng blur nếu được yêu cầu
            if use_blur:
                temp2 = output_path.replace('.jpg', '_temp2.jpg')
                current_path = add_gaussian_blur(current_path, temp2, blur_strength=random.randint(1, 2))
                temp_paths.append(temp2)

            # 3. Áp dụng LSB modification nếu được yêu cầu
            if use_lsb:
                temp3 = output_path.replace('.jpg', '_temp3.jpg')
                current_path = modify_lsb_steganography(current_path, temp3)
                temp_paths.append(temp3)

            # 4. Thêm watermark và lưu kết quả cuối cùng
            with Image.open(current_path) as img:
                wm_text = IMAGE_CONFIG.get('watermark_text', 'xehoi.pro')
                watermarked = self.add_watermark(img.convert('RGBA'), wm_text)
                watermarked.convert('RGB').save(output_path, format='JPEG', quality=92)

            # Xóa các file tạm thời
            for temp_path in temp_paths:
                if os.path.exists(temp_path) and temp_path != output_path:
                    os.remove(temp_path)

            self.logger.info(f"Đã áp dụng image modifications: {output_path}")
            return output_path

        except Exception as e:
            self.logger.error(f"Lỗi áp dụng image modifications cho {input_path}: {e}")
            # Cleanup temp files on error
            for temp_path in temp_paths:
                if os.path.exists(temp_path):
                    os.remove(temp_path)
            return None

    def score_image(self, img: Image.Image) -> float:
        """Chấm điểm ảnh dựa vào độ phân giải và tỷ lệ khung gần 16:9/4:3"""
        try:
            w, h = img.size
            res_score = (w * h) / (1920 * 1080)  # chuẩn hóa theo FullHD
            ratio = w / h if h else 1
            target_ratios = [16/9, 4/3, 3/2, 1.85]  # vài tỷ lệ phổ biến
            ratio_score = max(0.0, 1.0 - min(abs(ratio - r) for r in target_ratios) / 1.85)
            return max(0.0, res_score * 0.7 + ratio_score * 0.3)
        except Exception:
            return 0.0

    async def _fetch_one(self, session: aiohttp.ClientSession, url: str, timeout: int) -> Optional[bytes]:
        try:
            async with session.get(url, timeout=timeout) as resp:
                if resp.status != 200:
                    return None
                return await resp.read()
        except Exception:
            return None

    async def download_images_async(self, urls: List[str], save_dir: str, prefix: str = "img") -> List[str]:
        os.makedirs(save_dir, exist_ok=True)
        tasks = []
        timeout = aiohttp.ClientTimeout(total=self.timeout)
        async with aiohttp.ClientSession(timeout=timeout) as session:
            for idx, url in enumerate(urls):
                tasks.append(self._fetch_one(session, url, timeout=self.timeout))
            results = await asyncio.gather(*tasks, return_exceptions=False)
        paths: List[str] = []
        for idx, content in enumerate(results):
            if not content:
                continue
            path = os.path.join(save_dir, f"{prefix}_{idx+1:02d}.jpg")
            try:
                with open(path, 'wb') as f:
                    f.write(content)
                paths.append(path)
            except Exception:
                continue
        # print(f"✅ Đã tải {len(paths)} ảnh vào {save_dir}")
        # print(f"❌ Bỏ {len(urls) - len(paths)} ảnh do lỗi")
        # print(f"📋 Danh sách ảnh: {paths}")
        # exit()
        return paths


class ImageSearcher:
    def __init__(self, logger: Optional[logging.Logger] = None):
        self.logger = logger or logging.getLogger(__name__)
        self.provider_priority = IMAGE_CONFIG['provider_priority']
        self.google_key = IMAGE_CONFIG.get('google_api_key', '')
        self.google_cx = IMAGE_CONFIG.get('google_cx', '')

    def search_google(self, query: str, count: int = 10) -> List[str]:
        if not self.google_key or not self.google_cx:
            self.logger.warning('Chưa cấu hình GOOGLE_API_KEY/GOOGLE_CX')
            return []
        endpoint = 'https://www.googleapis.com/customsearch/v1'
        params = {
            'key': self.google_key,
            'cx': self.google_cx,
            'q': query,
            'searchType': 'image',
            'imgSize': 'XLARGE',
            'num': min(count, 10),  # Google CSE num tối đa 10
            'safe': 'medium',
        }
        try:
            r = requests.get(endpoint, params=params, timeout=15)
            if r.status_code != 200:
                self.logger.warning(f"Google CSE {r.status_code}: {r.text[:180]}")
                return []
            data = r.json()
            if 'error' in data:
                self.logger.warning(f"Google CSE error: {data['error']}")
                return []
            items = data.get('items', []) or []
            return [item.get('link') for item in items if item.get('link')]
        except Exception as e:
            self.logger.error(f"Lỗi search Google: {e}")
            return []

    def search(self, car_name: str, extra_terms: Optional[List[str]] = None, want: int = 5) -> List[str]:
        terms = ' '.join(extra_terms or [])
        # thêm từ khóa gợi ý để tìm ảnh ngoại thất rõ ràng
        # query = f"{car_name} {terms} exterior studio photo -interior -badge"
        query = f"{car_name}"
        urls: List[str] = []
        for provider in self.provider_priority:
            if provider == 'google':
                urls.extend(self.search_google(query, count=want * 3))
            urls = list(dict.fromkeys(urls))  # unique preserve order
            if len(urls) >= want:
                break
        return urls[:want]


def insert_images_into_markdown(md: str, images: List[ImageResult]) -> str:
    """Chèn ảnh vào nội dung Markdown theo các vị trí chính (nếu có)."""
    lines = md.splitlines()
    if not images:
        return md

    def insert_after_heading(title_keywords: List[str], img: ImageResult) -> bool:
        for i, line in enumerate(lines):
            normalized = line.strip('#').strip().lower()
            if any(k in normalized for k in title_keywords):
                lines.insert(i + 1, f"\n![Hình minh hoạ]({img.local_path})\n")
                return True
        return False

    imgs = images[:]
    # 1 - sau phần giới thiệu (đầu bài)
    lines.insert(1, f"\n![Hình mở bài]({imgs[0].local_path})\n")

    # 2 - gần bảng so sánh
    if len(imgs) > 1 and not insert_after_heading(['bảng so sánh', 'so sánh thông số'], imgs[1]):
        lines.append(f"\n![So sánh]({imgs[1].local_path})\n")

    # 3 - sau ưu nhược điểm
    if len(imgs) > 2 and not insert_after_heading(['ưu - nhược điểm', 'ưu/nhược điểm'], imgs[2]):
        lines.append(f"\n![Ưu nhược điểm]({imgs[2].local_path})\n")

    # 4 - trước kết luận
    if len(imgs) > 3 and not insert_after_heading(['kết luận'], imgs[3]):
        lines.append(f"\n![Kết luận]({imgs[3].local_path})\n")

    # 5 - bổ sung thêm cuối bài
    if len(imgs) > 4:
        lines.append(f"\n![Minh hoạ]({imgs[4].local_path})\n")

    return '\n'.join(lines)


def build_assets_paths(car1_name: str, car2_name: str) -> Tuple[str, str]:
    root = IMAGE_CONFIG['assets_root']
    car1_dir = os.path.join(root, 'images', car1_name.replace('/', '-'))
    car2_dir = os.path.join(root, 'images', car2_name.replace('/', '-'))
    return car1_dir, car2_dir


def fetch_and_process_images_for_car(car_name: str, want: int) -> List[ImageResult]:
    logger = logging.getLogger(__name__)
    searcher = ImageSearcher(logger)
    processor = ImageProcessor(logger)

    urls = searcher.search(car_name, want=want)
    results: List[ImageResult] = []
    car_dir, _ = build_assets_paths(car_name, car_name)

    # Tải async về raw/
    raw_dir = os.path.join(car_dir, 'raw')
    final_dir = os.path.join(car_dir, 'final')

    async def _run():
        return await processor.download_images_async(urls, save_dir=raw_dir, prefix="raw")

    try:
        downloaded_paths = asyncio.run(_run())
    except RuntimeError:
        # nếu đang trong event loop (ví dụ Jupyter), dùng loop khác
        loop = asyncio.new_event_loop()
        downloaded_paths = loop.run_until_complete(processor.download_images_async(urls, save_dir=raw_dir, prefix="raw"))
        loop.close()

    # Mở và chấm điểm ảnh, chọn top N
    scored = []
    for p in downloaded_paths:
        try:
            with Image.open(p) as im:
                score = processor.score_image(im)
            scored.append((score, p))
        except Exception:
            continue
    scored.sort(reverse=True, key=lambda x: x[0])
    selected = [p for _, p in scored[:want]]

    # Xử lý nền + watermark -> final/
    for idx, raw_path in enumerate(selected):
        final_path = os.path.join(final_dir, f"{idx+1:02d}.jpg")
        processed = processor.replace_background(raw_path, final_path)
        if not processed:
            continue
        results.append(ImageResult(url=urls[idx] if idx < len(urls) else raw_path, local_path=processed))

    return results
