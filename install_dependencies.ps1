# Script cài đặt dependencies cho hệ thống tạo bài viết so sánh xe nâng cao
# Chạy trong PowerShell với quyền Administrator

Write-Host "🚗 Cài đặt dependencies cho hệ thống tạo bài viết so sánh xe" -ForegroundColor Green
Write-Host "=" * 60

# Kiểm tra Python
Write-Host "Kiểm tra Python..." -ForegroundColor Yellow
try {
    $pythonVersion = python --version 2>&1
    Write-Host "✓ $pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "✗ Python không được tìm thấy. Vui lòng cài đặt Python trước." -ForegroundColor Red
    exit 1
}

# Kiểm tra pip
Write-Host "Kiểm tra pip..." -ForegroundColor Yellow
try {
    $pipVersion = pip --version 2>&1
    Write-Host "✓ $pipVersion" -ForegroundColor Green
} catch {
    Write-Host "✗ pip không được tìm thấy." -ForegroundColor Red
    exit 1
}

# Cài đặt dependencies mới
Write-Host "`nCài đặt dependencies mới..." -ForegroundColor Yellow

$newPackages = @(
    "boto3==1.35.0",
    "markdown==3.7"
)

foreach ($package in $newPackages) {
    Write-Host "Cài đặt $package..." -ForegroundColor Cyan
    try {
        pip install $package
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ Đã cài đặt $package" -ForegroundColor Green
        } else {
            Write-Host "✗ Lỗi cài đặt $package" -ForegroundColor Red
        }
    } catch {
        Write-Host "✗ Lỗi cài đặt $package" -ForegroundColor Red
    }
}

# Kiểm tra cài đặt
Write-Host "`nKiểm tra cài đặt..." -ForegroundColor Yellow

Write-Host "Kiểm tra boto3..." -ForegroundColor Cyan
try {
    python -c "import boto3; print(f'✓ boto3 version: {boto3.__version__}')"
} catch {
    Write-Host "✗ boto3 không được cài đặt đúng" -ForegroundColor Red
}

Write-Host "Kiểm tra markdown..." -ForegroundColor Cyan
try {
    python -c "import markdown; print(f'✓ markdown version: {markdown.__version__}')"
} catch {
    Write-Host "✗ markdown không được cài đặt đúng" -ForegroundColor Red
}

# Kiểm tra AWS credentials
Write-Host "`nKiểm tra cấu hình AWS..." -ForegroundColor Yellow
if (Test-Path ".env") {
    $envContent = Get-Content ".env" -Raw
    if ($envContent -match "ACCESS-KEY-ID" -and $envContent -match "SECRET-ACCESS-KEY" -and $envContent -match "BUCKET-NAME") {
        Write-Host "✓ AWS credentials đã được cấu hình trong .env" -ForegroundColor Green
    } else {
        Write-Host "⚠ AWS credentials chưa đầy đủ trong .env" -ForegroundColor Yellow
        Write-Host "Cần có: ACCESS-KEY-ID, SECRET-ACCESS-KEY, BUCKET-NAME" -ForegroundColor Yellow
    }
} else {
    Write-Host "⚠ File .env không tồn tại" -ForegroundColor Yellow
    Write-Host "Tạo file .env từ .env.example và cập nhật AWS credentials" -ForegroundColor Yellow
}

# Test kết nối S3
Write-Host "`nTest kết nối S3..." -ForegroundColor Yellow
try {
    python -c "
from s3_uploader import create_s3_uploader_from_env
uploader = create_s3_uploader_from_env()
if uploader and uploader.check_bucket_exists():
    print('✓ Kết nối S3 thành công')
else:
    print('✗ Không thể kết nối S3')
"
} catch {
    Write-Host "⚠ Không thể test S3 (có thể do thiếu credentials)" -ForegroundColor Yellow
}

Write-Host "`n" + "=" * 60
Write-Host "🎉 Hoàn thành cài đặt!" -ForegroundColor Green
Write-Host "`n📋 Các bước tiếp theo:" -ForegroundColor Cyan
Write-Host "1. Đảm bảo AWS credentials trong .env đã chính xác" -ForegroundColor White
Write-Host "2. Chạy test: python test_enhanced_system.py" -ForegroundColor White
Write-Host "3. Chạy hệ thống: python compare_cars.py" -ForegroundColor White
Write-Host "`n🔧 Nếu có lỗi, kiểm tra:" -ForegroundColor Cyan
Write-Host "- Kết nối internet" -ForegroundColor White
Write-Host "- AWS credentials và permissions" -ForegroundColor White
Write-Host "- Database connection" -ForegroundColor White
