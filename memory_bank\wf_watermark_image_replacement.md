# Workflow: Thay đổi Watermark từ Text sang Image

## Mô tả

Thay đổi chức năng watermark trong `image_pipeline.py` từ dạng text sang dạng hình ảnh sử dụng logo.

## Yêu cầu

1. **Loại bỏ**: Watermark dạng text hiện tại
2. **Thay thế bằng**: Watermark dạng hình ảnh sử dụng file `assets/logo.png`
3. **Vị trí**: <PERSON>óc dưới bên phải của ảnh
4. **Kích thước**: 10-15% chiều rộng ảnh gốc
5. **Tính năng**: Tự động tính toán kích thước, giữ tỷ lệ khung hình, độ trong suốt phù hợp

## Thay đổi thực hiện

### 1. Phương thức `add_watermark()` (dòng 135-162)

**Trước:**

- Sử dụng `ImageDraw.Draw()` để vẽ text
- Tạo nền mờ cho text
- Vị trí góc dưới phải với margin cố định

**Sau:**

- Mở file logo từ `assets/logo.png`
- Tính toán kích thước watermark (10-15% chiều rộng ảnh gốc)
- Giữ tỷ lệ khung hình của logo
- Áp dụng độ trong suốt phù hợp
- Paste logo vào góc dưới phải

### 2. Cập nhật import

- Không cần thay đổi import vì đã có `PIL.Image`

### 3. Xử lý lỗi

- Kiểm tra file logo có tồn tại không
- Fallback về watermark text nếu logo không tải được
- Log lỗi chi tiết

## Lợi ích

- Watermark chuyên nghiệp hơn với logo thương hiệu
- Tự động điều chỉnh kích thước theo ảnh gốc
- Giữ nguyên chất lượng và tỷ lệ logo
- Độ trong suốt phù hợp không che khuất nội dung

## File liên quan

- `image_pipeline.py`: File chính cần sửa đổi
- `assets/logo.png`: File logo watermark
- `config.py`: Cấu hình watermark (có thể cần cập nhật)
- `test_watermark.py`: Script test chức năng watermark

## Cách test

```bash
python test_watermark.py
```

Script sẽ tạo 3 ảnh test với kích thước khác nhau trong thư mục `test_output/`:

- `test_watermark_logo.jpg`: Ảnh 1920x1080 với watermark
- `test_watermark_small.jpg`: Ảnh 800x600 với watermark
- `test_watermark_large.jpg`: Ảnh 2560x1440 với watermark

## Trạng thái

✅ **HOÀN THÀNH** - Đã thay đổi thành công watermark từ text sang image
