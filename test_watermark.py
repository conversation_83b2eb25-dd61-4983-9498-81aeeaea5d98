"""
Script test để kiểm tra chức năng watermark mới
"""
import os
import logging
from PIL import Image
from image_pipeline import ImageProcessor

def test_watermark():
    """Test chức năng watermark với logo"""
    
    # Setup logging
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)
    
    # Tạo ImageProcessor
    processor = ImageProcessor(logger)
    
    # Tạo ảnh test (1920x1080)
    test_img = Image.new('RGB', (1920, 1080), color='lightblue')
    test_img_rgba = test_img.convert('RGBA')
    
    print("🧪 Bắt đầu test watermark...")
    
    # Test 1: Watermark với logo (nếu có)
    print("\n📝 Test 1: Watermark với logo")
    result_img = processor.add_watermark(test_img_rgba)
    
    # Lưu kết quả
    os.makedirs('test_output', exist_ok=True)
    output_path = 'test_output/test_watermark_logo.jpg'
    result_img.convert('RGB').save(output_path, quality=95)
    print(f"✅ Đã lưu ảnh test tại: {output_path}")
    
    # Test 2: Kiểm tra với ảnh kích thước khác
    print("\n📝 Test 2: Watermark với ảnh nhỏ hơn (800x600)")
    small_img = Image.new('RGB', (800, 600), color='lightgreen').convert('RGBA')
    result_small = processor.add_watermark(small_img)
    
    output_small = 'test_output/test_watermark_small.jpg'
    result_small.convert('RGB').save(output_small, quality=95)
    print(f"✅ Đã lưu ảnh test nhỏ tại: {output_small}")
    
    # Test 3: Kiểm tra với ảnh lớn hơn
    print("\n📝 Test 3: Watermark với ảnh lớn hơn (2560x1440)")
    large_img = Image.new('RGB', (2560, 1440), color='lightcoral').convert('RGBA')
    result_large = processor.add_watermark(large_img)
    
    output_large = 'test_output/test_watermark_large.jpg'
    result_large.convert('RGB').save(output_large, quality=95)
    print(f"✅ Đã lưu ảnh test lớn tại: {output_large}")
    
    # Kiểm tra file logo
    logo_path = os.path.join('assets', 'logo.png')
    if os.path.exists(logo_path):
        print(f"\n✅ File logo tồn tại: {logo_path}")
        try:
            with Image.open(logo_path) as logo:
                print(f"📏 Kích thước logo: {logo.size}")
                print(f"🎨 Mode logo: {logo.mode}")
        except Exception as e:
            print(f"❌ Lỗi đọc logo: {e}")
    else:
        print(f"\n❌ File logo không tồn tại: {logo_path}")
    
    print("\n🎉 Hoàn thành test watermark!")
    print("📂 Kiểm tra thư mục 'test_output' để xem kết quả")

if __name__ == "__main__":
    test_watermark()
