"""
Database Manager cho việ<PERSON> lưu trữ dữ liệu xe ô tô
"""
import mysql.connector
from mysql.connector import Error
import logging
from typing import List, Dict, Optional
from config import DB_CONFIG


class DatabaseManager:
    """Quản lý kết nối và thao tác với cơ sở dữ liệu MySQL"""

    def __init__(self):
        """Khởi tạo DatabaseManager"""
        self.connection = None
        self.cursor = None
        self.logger = logging.getLogger(__name__)

    def connect(self) -> bool:
        """
        Kết nối đến cơ sở dữ liệu MySQL

        Returns:
            bool: True nếu kết nối thành công, <PERSON>alse nếu thất bại
        """
        try:
            self.connection = mysql.connector.connect(**DB_CONFIG)
            if self.connection.is_connected():
                self.cursor = self.connection.cursor()
                self.logger.info("Kết nối database thành công")
                return True
        except Error as e:
            self.logger.error(f"Lỗi kết nối database: {e}")
            return False

    def disconnect(self):
        """Đóng kết nối database"""
        if self.cursor:
            self.cursor.close()
        if self.connection and self.connection.is_connected():
            self.connection.close()
            self.logger.info("Đã đóng kết nối database")

    def create_cars_table(self) -> bool:
        """
        Tạo bảng cars nếu chưa tồn tại

        Returns:
            bool: True nếu tạo bảng thành công
        """
        create_table_query = """
        CREATE TABLE IF NOT EXISTS cars (
            id INT AUTO_INCREMENT PRIMARY KEY,
            car_id VARCHAR(50) UNIQUE NOT NULL,
            car_name VARCHAR(500) NOT NULL,
            car_price BIGINT,
            car_price_text VARCHAR(100),
            car_link VARCHAR(500),
            page_number INT,
            scraped_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_car_id (car_id),
            INDEX idx_page_number (page_number),
            INDEX idx_scraped_at (scraped_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """

        try:
            self.cursor.execute(create_table_query)
            self.connection.commit()
            self.logger.info("Tạo bảng cars thành công")
            return True
        except Error as e:
            self.logger.error(f"Lỗi tạo bảng cars: {e}")
            return False

    def insert_car(self, car_data: Dict) -> bool:
        """
        Thêm thông tin xe vào database

        Args:
            car_data (Dict): Dữ liệu xe chứa car_id, car_name, car_price, etc.

        Returns:
            bool: True nếu thêm thành công
        """
        insert_query = """
        INSERT IGNORE INTO cars (car_id, car_name, car_price, car_price_text, car_link, page_number)
        VALUES (%s, %s, %s, %s, %s, %s)
        """

        try:
            values = (
                car_data.get('car_id'),
                car_data.get('car_name'),
                car_data.get('car_price'),
                car_data.get('car_price_text'),
                car_data.get('car_link'),
                car_data.get('page_number')
            )

            self.cursor.execute(insert_query, values)
            self.connection.commit()

            if self.cursor.rowcount > 0:
                self.logger.info(f"Đã thêm xe: {car_data.get('car_name')}")
                return True
            else:
                self.logger.warning(f"Xe đã tồn tại: {car_data.get('car_id')}")
                return False

        except Error as e:
            self.logger.error(f"Lỗi thêm xe vào database: {e}")
            return False

    def insert_cars_batch(self, cars_data: List[Dict]) -> int:
        """
        Thêm nhiều xe cùng lúc

        Args:
            cars_data (List[Dict]): Danh sách dữ liệu xe

        Returns:
            int: Số lượng xe được thêm thành công
        """
        insert_query = """
        INSERT IGNORE INTO cars (car_id, car_name, car_price, car_price_text, car_link, page_number)
        VALUES (%s, %s, %s, %s, %s, %s)
        """

        try:
            values_list = []
            for car_data in cars_data:
                values = (
                    car_data.get('car_id'),
                    car_data.get('car_name'),
                    car_data.get('car_price'),
                    car_data.get('car_price_text'),
                    car_data.get('car_link'),
                    car_data.get('page_number')
                )
                values_list.append(values)

            self.cursor.executemany(insert_query, values_list)
            self.connection.commit()

            inserted_count = self.cursor.rowcount
            self.logger.info(f"Đã thêm {inserted_count} xe vào database")
            return inserted_count

        except Error as e:
            self.logger.error(f"Lỗi thêm batch xe vào database: {e}")
            return 0

    def get_cars_count(self) -> int:
        """
        Lấy tổng số xe trong database

        Returns:
            int: Số lượng xe
        """
        try:
            self.cursor.execute("SELECT COUNT(*) FROM cars")
            result = self.cursor.fetchone()
            return result[0] if result else 0
        except Error as e:
            self.logger.error(f"Lỗi đếm số xe: {e}")
            return 0

    def create_comparisons_table(self) -> bool:
        """
        Tạo bảng car_comparisons nếu chưa tồn tại
        Returns:
            bool
        """
        create_table_query = (
            """
            CREATE TABLE IF NOT EXISTS car_comparisons (
                id INT AUTO_INCREMENT PRIMARY KEY,
                car1_name VARCHAR(500) NOT NULL,
                car2_name VARCHAR(500) NOT NULL,
                title VARCHAR(500) NOT NULL,
                content LONGTEXT NOT NULL,
                html_content LONGTEXT NULL,
                image_paths JSON NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_created_at (created_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """
        )
        try:
            self.cursor.execute(create_table_query)
            self.connection.commit()
            self.logger.info("Tạo bảng car_comparisons thành công")
            return True
        except Error as e:
            self.logger.error(f"Lỗi tạo bảng car_comparisons: {e}")
            return False

    def get_random_car(self) -> Optional[Dict]:
        """Lấy ngẫu nhiên 1 xe có giá hợp lệ (không null)"""
        try:
            self.cursor.execute(
                "SELECT car_id, car_name, car_price, car_price_text, car_link FROM cars WHERE car_price IS NOT NULL AND car_price < 800000000 ORDER BY RAND() LIMIT 1"
            )
            row = self.cursor.fetchone()
            if not row:
                return None
            return {
                'car_id': row[0],
                'car_name': row[1],
                'car_price': row[2],
                'car_price_text': row[3],
                'car_link': row[4],
            }
        except Error as e:
            self.logger.error(f"Lỗi lấy xe ngẫu nhiên: {e}")
            return None

    def get_random_car_by_price_range(self, base_price: int, tolerance: float = 0.2, exclude_car_id: Optional[str] = None) -> Optional[Dict]:
        """Lấy ngẫu nhiên 1 xe có giá trong khoảng ±tolerance so với base_price"""
        try:
            min_price = int(base_price * (1 - tolerance))
            max_price = int(base_price * (1 + tolerance))
            if exclude_car_id:
                query = (
                    "SELECT car_id, car_name, car_price, car_price_text, car_link FROM cars "
                    "WHERE car_price IS NOT NULL AND car_price BETWEEN %s AND %s AND car_id <> %s "
                    "ORDER BY RAND() LIMIT 1"
                )
                params = (min_price, max_price, exclude_car_id)
            else:
                query = (
                    "SELECT car_id, car_name, car_price, car_price_text, car_link FROM cars "
                    "WHERE car_price IS NOT NULL AND car_price BETWEEN %s AND %s "
                    "ORDER BY RAND() LIMIT 1"
                )
                params = (min_price, max_price)
            self.cursor.execute(query, params)
            row = self.cursor.fetchone()
            if not row:
                return None
            return {
                'car_id': row[0],
                'car_name': row[1],
                'car_price': row[2],
                'car_price_text': row[3],
                'car_link': row[4],
            }
        except Error as e:
            self.logger.error(f"Lỗi lấy xe theo khoảng giá: {e}")
            return None

    def save_comparison(self, car1_name: str, car2_name: str, title: str, content: str) -> Optional[int]:
        """Lưu bài viết so sánh vào bảng car_comparisons, trả về ID bản ghi"""
        try:
            insert_sql = (
                "INSERT INTO car_comparisons (car1_name, car2_name, title, content) VALUES (%s, %s, %s, %s)"
            )
            self.cursor.execute(insert_sql, (car1_name, car2_name, title, content))
            self.connection.commit()
            comp_id = self.cursor.lastrowid
            self.logger.info("Đã lưu bài viết so sánh vào cơ sở dữ liệu")
            return comp_id
        except Error as e:
            self.logger.error(f"Lỗi lưu bài viết so sánh: {e}")
            return None

    def comparison_exists(self, car1_name: str, car2_name: str) -> bool:
        """Kiểm tra đã có bài viết so sánh giữa 2 xe (không phân biệt thứ tự) hay chưa"""
        try:
            sql = (
                "SELECT 1 FROM car_comparisons WHERE (car1_name=%s AND car2_name=%s) OR (car1_name=%s AND car2_name=%s) LIMIT 1"
            )
            self.cursor.execute(sql, (car1_name, car2_name, car2_name, car1_name))
            return self.cursor.fetchone() is not None
        except Error as e:
            self.logger.error(f"Lỗi kiểm tra trùng bài viết so sánh: {e}")
            return False

    def update_comparison_images(self, comparison_id: int, image_paths: List[str]) -> bool:
        """Cập nhật danh sách ảnh (local paths) dạng JSON cho bài viết"""
        try:
            import json
            sql = "UPDATE car_comparisons SET image_paths=%s WHERE id=%s"
            self.cursor.execute(sql, (json.dumps(image_paths, ensure_ascii=False), comparison_id))
            self.connection.commit()
            return True
        except Error as e:
            self.logger.error(f"Lỗi cập nhật ảnh cho bài viết: {e}")
            return False

    def update_comparison_content(self, comparison_id: int, content: str) -> bool:
        """Cập nhật nội dung bài viết (Markdown) cho bản ghi car_comparisons"""
        try:
            sql = "UPDATE car_comparisons SET content=%s WHERE id=%s"
            self.cursor.execute(sql, (content, comparison_id))
            self.connection.commit()
            return True
        except Error as e:
            self.logger.error(f"Lỗi cập nhật nội dung bài viết: {e}")
            return False

    def update_comparison_html_content(self, comparison_id: int, html_content: str) -> bool:
        """Cập nhật nội dung HTML cho bản ghi car_comparisons"""
        try:
            sql = "UPDATE car_comparisons SET html_content=%s WHERE id=%s"
            self.cursor.execute(sql, (html_content, comparison_id))
            self.connection.commit()
            return True
        except Error as e:
            self.logger.error(f"Lỗi cập nhật HTML content: {e}")
            return False

    def create_car_images_table(self) -> bool:
        """
        Tạo bảng car_images để lưu trữ URLs ảnh từ S3
        Returns:
            bool: True nếu tạo bảng thành công
        """
        create_table_query = """
        CREATE TABLE IF NOT EXISTS car_images (
            id INT AUTO_INCREMENT PRIMARY KEY,
            car_id VARCHAR(50) NOT NULL,
            car_name VARCHAR(500) NOT NULL,
            image_url VARCHAR(1000) NOT NULL,
            s3_key VARCHAR(500) NOT NULL,
            local_path VARCHAR(500) NULL,
            uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_car_id (car_id),
            INDEX idx_car_name (car_name),
            INDEX idx_uploaded_at (uploaded_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """
        try:
            self.cursor.execute(create_table_query)
            self.connection.commit()
            self.logger.info("Tạo bảng car_images thành công")
            return True
        except Error as e:
            self.logger.error(f"Lỗi tạo bảng car_images: {e}")
            return False

    def save_car_image(self, car_id: str, car_name: str, image_url: str, s3_key: str, local_path: str = None) -> Optional[int]:
        """Lưu thông tin ảnh xe đã upload lên S3"""
        try:
            insert_sql = """
            INSERT INTO car_images (car_id, car_name, image_url, s3_key, local_path)
            VALUES (%s, %s, %s, %s, %s)
            """
            self.cursor.execute(insert_sql, (car_id, car_name, image_url, s3_key, local_path))
            self.connection.commit()
            image_id = self.cursor.lastrowid
            self.logger.info(f"Đã lưu thông tin ảnh S3: {s3_key}")
            return image_id
        except Error as e:
            self.logger.error(f"Lỗi lưu thông tin ảnh S3: {e}")
            return None

    def get_car_images_by_name(self, car_name: str, limit: int = 2) -> List[dict]:
        """Lấy danh sách ảnh của xe theo tên"""
        try:
            sql = """
            SELECT id, car_id, car_name, image_url, s3_key, local_path, uploaded_at
            FROM car_images
            WHERE car_name = %s
            ORDER BY uploaded_at DESC
            LIMIT %s
            """
            self.cursor.execute(sql, (car_name, limit))
            results = self.cursor.fetchall()

            images = []
            for row in results:
                images.append({
                    'id': row[0],
                    'car_id': row[1],
                    'car_name': row[2],
                    'image_url': row[3],
                    's3_key': row[4],
                    'local_path': row[5],
                    'uploaded_at': row[6]
                })
            return images
        except Error as e:
            self.logger.error(f"Lỗi lấy danh sách ảnh xe: {e}")
            return []
