"""
Script test cho hệ thống tạo bài viết so sánh xe nâng cao
"""
import json
import os
import sys


def test_json_parsing():
    """Test JSON parsing logic"""
    print("=== Test JSON Parsing ===")

    # Test case 1: Valid JSON
    content1 = '''Đây là response từ AI:
{
    "title": "So sánh Toyota Camry và Honda Accord 2024",
    "content": "## Giới thiệu\\n\\nToyota Camry và Honda Accord là hai mẫu sedan hạng D...\\n\\n[ANH_XE_SO_1]\\n\\n### Thiết kế\\n\\n[ANH_XE_SO_2]\\n\\n## Kết luận"
}
Cảm ơn bạn!'''

    try:
        start_idx = content1.find('{')
        end_idx = content1.rfind('}') + 1
        if start_idx != -1 and end_idx > start_idx:
            json_str = content1[start_idx:end_idx]
            parsed_data = json.loads(json_str)
            print("✓ Test 1 PASSED: Valid <PERSON> parsed successfully")
            print(f"  Title: {parsed_data['title'][:50]}...")
            print(f"  Content length: {len(parsed_data['content'])} chars")
        else:
            print("✗ Test 1 FAILED: JSON not found")
    except Exception as e:
        print(f"✗ Test 1 FAILED: {e}")

    # Test case 2: Invalid JSON (fallback)
    content2 = "Đây là nội dung không có JSON hợp lệ"
    try:
        start_idx = content2.find('{')
        end_idx = content2.rfind('}') + 1
        if start_idx == -1 or end_idx <= start_idx:
            print("✓ Test 2 PASSED: Fallback triggered for invalid JSON")
        else:
            print("✗ Test 2 FAILED: Should trigger fallback")
    except Exception as e:
        print(f"✗ Test 2 FAILED: {e}")


def test_placeholder_replacement():
    """Test placeholder replacement logic"""
    print("\n=== Test Placeholder Replacement ===")

    # Test với nhiều placeholder (theo yêu cầu mới)
    content = """
## So sánh Toyota Camry và Honda Accord

### Thiết kế ngoại thất

[ANH_XE_SO_1]

Toyota Camry có thiết kế thể thao và hiện đại...

[ANH_XE_SO_1]

### Nội thất

[ANH_XE_SO_2]

Honda Accord nổi bật với không gian rộng rãi...

[ANH_XE_SO_2]

### So sánh trực tiếp

[ANH_XE_SO_1] vs [ANH_XE_SO_2]

Kết luận cuối cùng.
"""

    # Mock S3 URLs
    car1_url = "https://xehoipro.s3.ap-southeast-1.amazonaws.com/car_images/20241213_143022_abc123.jpg"
    car2_url = "https://xehoipro.s3.ap-southeast-1.amazonaws.com/car_images/20241213_143045_def456.jpg"

    # Đếm số placeholder ban đầu
    car1_count = content.count('[ANH_XE_SO_1]')
    car2_count = content.count('[ANH_XE_SO_2]')
    print(f"  Số placeholder ban đầu: [ANH_XE_SO_1]={car1_count}, [ANH_XE_SO_2]={car2_count}")

    # Test replacement - thay thế TẤT CẢ
    result = content.replace('[ANH_XE_SO_1]', f'![Toyota Camry]({car1_url})')
    result = result.replace('[ANH_XE_SO_2]', f'![Honda Accord]({car2_url})')

    # Kiểm tra kết quả
    remaining_car1 = result.count('[ANH_XE_SO_1]')
    remaining_car2 = result.count('[ANH_XE_SO_2]')

    if remaining_car1 == 0 and remaining_car2 == 0:
        print("✓ Test PASSED: Tất cả placeholders đã được thay thế")
        print(f"  Đã thay thế {car1_count} lần [ANH_XE_SO_1]")
        print(f"  Đã thay thế {car2_count} lần [ANH_XE_SO_2]")
        print(f"  Car 1 URL: {car1_url}")
        print(f"  Car 2 URL: {car2_url}")
    else:
        print(f"✗ Test FAILED: Còn {remaining_car1 + remaining_car2} placeholder chưa được thay thế")


def test_markdown_to_html():
    """Test Markdown to HTML conversion"""
    print("\n=== Test Markdown to HTML ===")

    markdown_content = """
# So sánh Toyota Camry và Honda Accord

## Thiết kế

![Toyota Camry](https://example.com/camry.jpg)

Toyota Camry có **thiết kế thể thao** và hiện đại với:

- Lưới tản nhiệt cỡ lớn
- Đèn LED sắc nét
- Đường nét *mạnh mẽ*

## Động cơ

| Xe | Động cơ | Công suất |
|---|---|---|
| Camry | 2.5L | 203 HP |
| Accord | 1.5L Turbo | 192 HP |

```python
# Code example
def compare_cars():
    return "Camry vs Accord"
```

## Kết luận

> Toyota Camry và Honda Accord đều là những lựa chọn tuyệt vời.
"""

    try:
        # Simple HTML conversion (without markdown library)
        html_simple = markdown_content.replace('\n', '<br>\n')
        html_simple = html_simple.replace('**', '<strong>').replace('**', '</strong>')
        html_simple = html_simple.replace('*', '<em>').replace('*', '</em>')

        print("✓ Test PASSED: Basic Markdown conversion")
        print(f"  Original length: {len(markdown_content)} chars")
        print(f"  HTML length: {len(html_simple)} chars")

        # Test with actual markdown library if available
        try:
            import markdown
            md = markdown.Markdown(extensions=['extra', 'codehilite', 'toc'])
            html_full = md.convert(markdown_content)
            print("✓ Advanced Markdown conversion available")
            print(f"  Full HTML length: {len(html_full)} chars")
        except ImportError:
            print("! Markdown library not installed (expected in test)")

    except Exception as e:
        print(f"✗ Test FAILED: {e}")


def test_s3_url_extraction():
    """Test S3 key extraction from URL"""
    print("\n=== Test S3 URL Processing ===")

    test_urls = [
        "https://xehoipro.s3.ap-southeast-1.amazonaws.com/car_images/20241213_143022_abc123.jpg",
        "https://bucket.s3.region.amazonaws.com/folder/subfolder/image.jpg",
        "https://example.com/simple.jpg"
    ]

    for url in test_urls:
        try:
            # Extract S3 key logic
            s3_key = '/'.join(url.split('/')[3:]) if '/' in url else url.split('/')[-1]
            print(f"✓ URL: {url}")
            print(f"  S3 Key: {s3_key}")
        except Exception as e:
            print(f"✗ Failed to process URL {url}: {e}")


def test_database_schema():
    """Test database schema validation"""
    print("\n=== Test Database Schema ===")

    # Test car_images table structure
    car_image_fields = [
        'id', 'car_id', 'car_name', 'image_url',
        's3_key', 'local_path', 'uploaded_at'
    ]

    print("✓ car_images table fields:")
    for field in car_image_fields:
        print(f"  - {field}")

    # Test car_comparisons updates
    comparison_fields = [
        'id', 'car1_name', 'car2_name', 'title',
        'content', 'html_content', 'image_paths', 'created_at'
    ]

    print("✓ car_comparisons table fields:")
    for field in comparison_fields:
        print(f"  - {field}")


def main():
    """Run all tests"""
    print("🚗 Testing Enhanced Car Comparison System")
    print("=" * 50)

    test_json_parsing()
    test_placeholder_replacement()
    test_markdown_to_html()
    test_s3_url_extraction()
    test_database_schema()

    print("\n" + "=" * 50)
    print("✅ All tests completed!")
    print("\n📋 Next steps:")
    print("1. Install dependencies: pip install boto3 markdown")
    print("2. Configure AWS credentials in .env")
    print("3. Run: python compare_cars.py")


if __name__ == "__main__":
    main()
