#!/usr/bin/env python3
"""
Quick Test Tool cho Image Pipeline
Chạy nhanh để test chức năng cơ bản

Usage: python quick_test_image.py
"""

import logging
import sys
from database_manager import DatabaseManager
from image_pipeline import fetch_and_process_images_for_car


def main():
    # Setup logging đơn giản
    logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
    logger = logging.getLogger(__name__)

    print("🔧 QUICK IMAGE PIPELINE TEST")
    print("="*50)

    # Kết nối database
    db = DatabaseManager()

    try:
        db.connect()
        print("✅ Kết nối database thành công")

        # Lấy xe ngẫu nhiên
        car = db.get_random_car()
        if not car:
            print("❌ Không thể lấy xe từ database!")
            return

        print(f"\n🚗 Xe được chọn: {car['car_name']}")
        print(f"💰 Giá: {car['car_price']:,} VNĐ")

        # Test tìm kiếm ảnh
        print(f"\n🔍 Đang tìm kiếm ảnh cho: {car['car_name']}")

        try:
            results = fetch_and_process_images_for_car(car['car_name'], want=5)

            print(f"\n✅ Kết quả: {len(results)} ảnh được xử lý")
            for i, result in enumerate(results, 1):
                print(f"  {i}. {result.local_path}")

        except Exception as e:
            print(f"❌ Lỗi xử lý ảnh: {e}")

    except Exception as e:
        print(f"❌ Lỗi: {e}")

    finally:
        db.disconnect()
        print("\n🔌 Đã ngắt kết nối database")


if __name__ == "__main__":
    main()
