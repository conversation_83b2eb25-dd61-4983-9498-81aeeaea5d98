"""
Module để upload ảnh lên AWS S3
"""
import os
import logging
import boto3
from botocore.exceptions import ClientError, NoCredentialsError
from typing import Optional, List
from datetime import datetime
import uuid


class S3Uploader:
    """Class để upload ảnh lên AWS S3"""
    
    def __init__(self, access_key: str, secret_key: str, bucket_name: str, region: str = 'ap-southeast-1'):
        """
        Khởi tạo S3 uploader
        
        Args:
            access_key: AWS Access Key ID
            secret_key: AWS Secret Access Key  
            bucket_name: Tên S3 bucket
            region: AWS region (mặc định ap-southeast-1)
        """
        self.bucket_name = bucket_name
        self.region = region
        self.logger = logging.getLogger(__name__)
        
        try:
            self.s3_client = boto3.client(
                's3',
                aws_access_key_id=access_key,
                aws_secret_access_key=secret_key,
                region_name=region
            )
            self.logger.info("Khởi tạo S3 client thành công")
        except Exception as e:
            self.logger.error(f"Lỗi khởi tạo S3 client: {e}")
            raise

    def upload_file(self, local_file_path: str, s3_key: str = None) -> Optional[str]:
        """
        Upload một file lên S3
        
        Args:
            local_file_path: Đường dẫn file local
            s3_key: Key trong S3 (nếu None sẽ tự tạo)
            
        Returns:
            URL của file trên S3 hoặc None nếu lỗi
        """
        if not os.path.exists(local_file_path):
            self.logger.error(f"File không tồn tại: {local_file_path}")
            return None
            
        if s3_key is None:
            # Tạo S3 key tự động
            file_ext = os.path.splitext(local_file_path)[1]
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            unique_id = str(uuid.uuid4())[:8]
            s3_key = f"car_images/{timestamp}_{unique_id}{file_ext}"
        
        try:
            # Upload file
            self.s3_client.upload_file(
                local_file_path, 
                self.bucket_name, 
                s3_key,
                ExtraArgs={'ContentType': self._get_content_type(local_file_path)}
            )
            
            # Tạo URL
            url = f"https://{self.bucket_name}.s3.{self.region}.amazonaws.com/{s3_key}"
            self.logger.info(f"Upload thành công: {local_file_path} -> {url}")
            return url
            
        except FileNotFoundError:
            self.logger.error(f"File không tìm thấy: {local_file_path}")
            return None
        except NoCredentialsError:
            self.logger.error("AWS credentials không hợp lệ")
            return None
        except ClientError as e:
            self.logger.error(f"Lỗi S3 client: {e}")
            return None
        except Exception as e:
            self.logger.error(f"Lỗi upload file: {e}")
            return None

    def upload_multiple_files(self, file_paths: List[str]) -> List[dict]:
        """
        Upload nhiều file cùng lúc
        
        Args:
            file_paths: Danh sách đường dẫn file
            
        Returns:
            List các dict chứa thông tin upload: {'local_path', 'url', 's3_key', 'success'}
        """
        results = []
        
        for file_path in file_paths:
            if not os.path.exists(file_path):
                self.logger.warning(f"File không tồn tại, bỏ qua: {file_path}")
                results.append({
                    'local_path': file_path,
                    'url': None,
                    's3_key': None,
                    'success': False,
                    'error': 'File không tồn tại'
                })
                continue
                
            # Tạo S3 key
            file_ext = os.path.splitext(file_path)[1]
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            unique_id = str(uuid.uuid4())[:8]
            s3_key = f"car_images/{timestamp}_{unique_id}{file_ext}"
            
            url = self.upload_file(file_path, s3_key)
            
            results.append({
                'local_path': file_path,
                'url': url,
                's3_key': s3_key if url else None,
                'success': url is not None,
                'error': None if url else 'Upload failed'
            })
            
        return results

    def _get_content_type(self, file_path: str) -> str:
        """Xác định content type dựa trên extension"""
        ext = os.path.splitext(file_path)[1].lower()
        content_types = {
            '.jpg': 'image/jpeg',
            '.jpeg': 'image/jpeg', 
            '.png': 'image/png',
            '.gif': 'image/gif',
            '.webp': 'image/webp'
        }
        return content_types.get(ext, 'application/octet-stream')

    def delete_file(self, s3_key: str) -> bool:
        """
        Xóa file trên S3
        
        Args:
            s3_key: Key của file trên S3
            
        Returns:
            True nếu xóa thành công
        """
        try:
            self.s3_client.delete_object(Bucket=self.bucket_name, Key=s3_key)
            self.logger.info(f"Đã xóa file S3: {s3_key}")
            return True
        except ClientError as e:
            self.logger.error(f"Lỗi xóa file S3: {e}")
            return False

    def check_bucket_exists(self) -> bool:
        """Kiểm tra bucket có tồn tại không"""
        try:
            self.s3_client.head_bucket(Bucket=self.bucket_name)
            return True
        except ClientError:
            return False


def create_s3_uploader_from_env() -> Optional[S3Uploader]:
    """
    Tạo S3Uploader từ biến môi trường
    
    Returns:
        S3Uploader instance hoặc None nếu thiếu config
    """
    access_key = os.getenv('ACCESS-KEY-ID')
    secret_key = os.getenv('SECRET-ACCESS-KEY') 
    bucket_name = os.getenv('BUCKET-NAME')
    
    if not all([access_key, secret_key, bucket_name]):
        logging.getLogger(__name__).error("Thiếu AWS credentials trong .env")
        return None
        
    try:
        return S3Uploader(access_key, secret_key, bucket_name)
    except Exception as e:
        logging.getLogger(__name__).error(f"Lỗi tạo S3Uploader: {e}")
        return None
