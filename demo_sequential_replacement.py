"""
Demo script để minh họa thay thế placeholder tuần tự với ảnh khác nhau
"""

def demo_sequential_replacement():
    """Demo thay thế placeholder tuần tự"""
    print("🚗 Demo: Sequential Image Placeholder Replacement")
    print("=" * 60)
    
    # Content mẫu với nhiều placeholder
    content = """
# So sánh Toyota Camry vs Honda Accord 2024

## Thiết kế ngoại thất

[ANH_XE_SO_1]

Toyota Camry 2024 có thiết kế thể thao và hiện đại với lưới tản nhiệt cỡ lớn.

## Nội thất và tiện nghi

[ANH_XE_SO_2]

Honda Accord nổi bật với không gian rộng rãi và công nghệ hiện đại.

## So sánh chi tiết

### Mặt trước

[ANH_XE_SO_1]

Cam<PERSON> có đèn LED sắc nét, trong khi Accord có thiết kế thanh lịch hơn.

[ANH_XE_SO_2]

### K<PERSON><PERSON> lái

[ANH_XE_SO_1] vs [ANH_XE_SO_2]

Cả hai đều có bảng điều khiển hiện đại với màn hình cảm ứng lớn.

## Kết luận

Camry và Accord đều là những lựa chọn tuyệt vời trong phân khúc sedan hạng D.
"""
    
    print("📝 Content gốc:")
    print(content)
    print("\n" + "=" * 60)
    
    # Mock database images
    car1_images = [
        {"image_url": "https://xehoipro.s3.ap-southeast-1.amazonaws.com/car_images/camry_exterior.jpg"},
        {"image_url": "https://xehoipro.s3.ap-southeast-1.amazonaws.com/car_images/camry_front.jpg"},
        {"image_url": "https://xehoipro.s3.ap-southeast-1.amazonaws.com/car_images/camry_interior.jpg"},
    ]
    
    car2_images = [
        {"image_url": "https://xehoipro.s3.ap-southeast-1.amazonaws.com/car_images/accord_exterior.jpg"},
        {"image_url": "https://xehoipro.s3.ap-southeast-1.amazonaws.com/car_images/accord_front.jpg"},
        {"image_url": "https://xehoipro.s3.ap-southeast-1.amazonaws.com/car_images/accord_interior.jpg"},
    ]
    
    # Đếm placeholder
    car1_count = content.count('[ANH_XE_SO_1]')
    car2_count = content.count('[ANH_XE_SO_2]')
    
    print(f"📊 Thống kê:")
    print(f"  - Placeholder [ANH_XE_SO_1]: {car1_count} lần")
    print(f"  - Placeholder [ANH_XE_SO_2]: {car2_count} lần")
    print(f"  - Ảnh xe 1 có sẵn: {len(car1_images)}")
    print(f"  - Ảnh xe 2 có sẵn: {len(car2_images)}")
    
    print(f"\n🔄 Quá trình thay thế:")
    
    result = content
    
    # Thay thế tuần tự cho xe 1
    print(f"\n🚗 Thay thế [ANH_XE_SO_1] (Toyota Camry):")
    car1_index = 0
    for i in range(car1_count):
        current_image = car1_images[car1_index % len(car1_images)]
        image_url = current_image['image_url']
        image_name = image_url.split('/')[-1].replace('.jpg', '')
        
        result = result.replace('[ANH_XE_SO_1]', f'![Toyota Camry - {image_name}]({image_url})', 1)
        print(f"  Lần {i+1}: {image_name} -> {image_url}")
        
        car1_index += 1
    
    # Thay thế tuần tự cho xe 2
    print(f"\n🚙 Thay thế [ANH_XE_SO_2] (Honda Accord):")
    car2_index = 0
    for i in range(car2_count):
        current_image = car2_images[car2_index % len(car2_images)]
        image_url = current_image['image_url']
        image_name = image_url.split('/')[-1].replace('.jpg', '')
        
        result = result.replace('[ANH_XE_SO_2]', f'![Honda Accord - {image_name}]({image_url})', 1)
        print(f"  Lần {i+1}: {image_name} -> {image_url}")
        
        car2_index += 1
    
    print(f"\n" + "=" * 60)
    print("✅ Kết quả sau khi thay thế:")
    print(result)
    
    # Kiểm tra kết quả
    remaining_placeholders = result.count('[ANH_XE_SO_1]') + result.count('[ANH_XE_SO_2]')
    
    print(f"\n📈 Kết quả:")
    if remaining_placeholders == 0:
        print("✅ Thành công: Tất cả placeholder đã được thay thế")
        
        # Đếm số ảnh unique
        unique_car1_urls = set()
        unique_car2_urls = set()
        
        for img in car1_images:
            if img['image_url'] in result:
                unique_car1_urls.add(img['image_url'])
                
        for img in car2_images:
            if img['image_url'] in result:
                unique_car2_urls.add(img['image_url'])
        
        print(f"✅ Xe 1: Sử dụng {len(unique_car1_urls)} ảnh khác nhau")
        print(f"✅ Xe 2: Sử dụng {len(unique_car2_urls)} ảnh khác nhau")
        print("✅ Không có ảnh nào bị lặp lại!")
        
    else:
        print(f"❌ Còn {remaining_placeholders} placeholder chưa được thay thế")
    
    print(f"\n🎯 Lợi ích của phương pháp mới:")
    print("  - Mỗi placeholder được thay bằng ảnh khác nhau")
    print("  - Bài viết đa dạng hơn, không monotone")
    print("  - Tận dụng tối đa ảnh có sẵn")
    print("  - Tự động lặp lại nếu placeholder nhiều hơn ảnh")


if __name__ == "__main__":
    demo_sequential_replacement()
